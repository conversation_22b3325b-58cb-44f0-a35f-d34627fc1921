import React, { useState } from 'react';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Search } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { useLogsData } from '../../../hooks/useLogsData';
import { Loader2 } from 'lucide-react';

const Logs: React.FC = () => {
  const [idVendor, setIdVendor] = useState('');
  const [nomorESeal, setNomorESeal] = useState('');
  const [token, setToken] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  const { data: logsData, total, totalPages, loading, error, refetch } = useLogsData({
    idVendor,
    nomorESeal,
    token,
    page,
    limit,
  });

  const handleSearch = () => {
    setPage(1);
    refetch();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Logs</h1>
      </div>

      <div className="bg-white p-4 rounded-lg border space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input placeholder="ID Vendor" value={idVendor} onChange={(e) => setIdVendor(e.target.value)} />
          <Input placeholder="Nomor E-Seal" value={nomorESeal} onChange={(e) => setNomorESeal(e.target.value)} />
          <Input placeholder="Token (opsional)" value={token} onChange={(e) => setToken(e.target.value)} />
        </div>
        <div className="flex justify-end">
          <Button onClick={handleSearch} disabled={loading}>
            {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
            Cari Logs
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead>Timestamp</TableHead>
                <TableHead>E-Seal</TableHead>
                <TableHead>Aktivitas</TableHead>
                <TableHead>Lokasi</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8"><Loader2 className="mx-auto h-6 w-6 animate-spin" /></TableCell></TableRow>
              ) : error ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-red-500">{error}</TableCell></TableRow>
              ) : logsData.length === 0 ? (
                <TableRow><TableCell colSpan={5} className="text-center py-8 text-gray-500">Data tidak ditemukan. Silakan lakukan pencarian.</TableCell></TableRow>
              ) : (
                logsData.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{new Date(log.timestamp).toLocaleString('id-ID')}</TableCell>
                    <TableCell>{log.esealName} ({log.esealIMEI})</TableCell>
                    <TableCell>{log.activity}</TableCell>
                    <TableCell>{log.location.address || `${log.location.latitude}, ${log.location.longitude}`}</TableCell>
                    <TableCell>{log.status}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {total > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Menampilkan {((page - 1) * limit) + 1} sampai {Math.min(page * limit, total)} dari {total} entri
          </span>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setPage(p => Math.max(p - 1, 1))} disabled={page === 1}>
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">{page}</span>
            <Button variant="outline" size="sm" onClick={() => setPage(p => Math.min(p + 1, totalPages))} disabled={page === totalPages}>
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Logs;
