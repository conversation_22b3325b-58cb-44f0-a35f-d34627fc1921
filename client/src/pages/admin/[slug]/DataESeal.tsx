import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { Search, Eye, Loader2, Plus, X, Edit } from 'lucide-react';
import DetailESeal from './DetailESeal';
import { useESealData, type ESealData } from '../../../hooks/useESealData';
import { VENDOR_OPTIONS, ESEAL_TYPES } from '@shared';

interface ESealFormData {
  idVendor: string;
  merk: string;
  model: string;
  noImei: string;
  tipe: string;
  token: string;
  gpsDeviceId: string;
}

interface GpsDevice {
  vehicleId: number;
  vehicleName: string;
  deviceId: string;
  groupName: string;
  online: boolean;
  lat: number;
  lng: number;
  updateTime: string;
}

// Modal Component for Adding/Editing E-Seal
const TambahDataModal = ({
  isOpen,
  onClose,
  onSubmit,
  slug,
  loading,
  editData,
  isEdit = false,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ESealFormData, slug: string) => void;
  slug: string;
  loading: boolean;
  editData?: ESealData;
  isEdit?: boolean;
}) => {
  const [formData, setFormData] = useState<ESealFormData>({
    idVendor: '',
    merk: '',
    model: '',
    noImei: '',
    tipe: '',
    token: '',
    gpsDeviceId: '',
  });

  const [gpsDevices, setGpsDevices] = useState<GpsDevice[]>([]);
  const [loadingGpsDevices, setLoadingGpsDevices] = useState(false);

  // Populate form data when editing
  useEffect(() => {
    if (isEdit && editData) {
      console.log('🔍 Populating edit form with data:', editData);
      console.log('🔍 GPS Device ID from editData:', editData.gpsDeviceId);
      setFormData({
        idVendor: editData.idVendor || '',
        merk: editData.merk || '',
        model: editData.model || '',
        noImei: editData.noImei || '',
        tipe: editData.tipe || '',
        token: '', // Don't populate token for security
        gpsDeviceId: editData.gpsDeviceId || '',
      });
    } else {
      // Reset form when not editing
      setFormData({
        idVendor: '',
        merk: '',
        model: '',
        noImei: '',
        tipe: '',
        token: '',
        gpsDeviceId: '',
      });
    }
  }, [isEdit, editData, isOpen]);

  // Fetch GPS devices when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchGpsDevices();
    }
  }, [isOpen]);

  // Re-validate GPS device selection after GPS devices are loaded
  useEffect(() => {
    console.log('🔍 Select render state:', {
      isEdit,
      hasEditData: !!editData,
      gpsDevicesCount: gpsDevices.length,
      formGpsDeviceId: formData.gpsDeviceId,
      loadingGpsDevices
    });

    if (isEdit && editData && gpsDevices.length > 0 && formData.gpsDeviceId) {
      const selectedDevice = gpsDevices.find(d => d.deviceId === formData.gpsDeviceId);
      console.log('🔍 Validating GPS device selection after devices loaded:');
      console.log('🔍 Looking for deviceId:', formData.gpsDeviceId);
      console.log('🔍 Found device:', selectedDevice);

      if (!selectedDevice) {
        console.log('⚠️ GPS device not found in current list, clearing selection');
        setFormData(prev => ({ ...prev, gpsDeviceId: '' }));
      } else {
        console.log('✅ GPS device found and validated:', selectedDevice.vehicleName);
      }
    }
  }, [gpsDevices, isEdit, editData, formData.gpsDeviceId, loadingGpsDevices]);

  const fetchGpsDevices = async () => {
    setLoadingGpsDevices(true);
    try {
      console.log('📍 Fetching GPS devices...');
      const response = await fetch('/api/beacukai/gps/devices');
      const result = await response.json();

      if (response.ok && result.success) {
        setGpsDevices(result.data);
        console.log('✅ GPS devices loaded:', result.data.length);
        console.log('📍 GPS devices:', result.data.map((d: GpsDevice) => ({ deviceId: d.deviceId, vehicleName: d.vehicleName })));
      } else {
        console.error('❌ Failed to fetch GPS devices:', result.error);
        setGpsDevices([]);
      }
    } catch (error) {
      console.error('❌ Error fetching GPS devices:', error);
      setGpsDevices([]);
    } finally {
      setLoadingGpsDevices(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData, slug);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">
              {isEdit ? 'Edit Data E-Seal' : 'Tambah Data E-Seal'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ID Vendor <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.idVendor}
                onValueChange={(value) => setFormData({ ...formData, idVendor: value })}
                required
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih vendor" />
                </SelectTrigger>
                <SelectContent>
                  {VENDOR_OPTIONS.map((vendor) => (
                    <SelectItem key={vendor.value} value={vendor.value}>
                      {vendor.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Merk <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan merk E-Seal"
                value={formData.merk}
                onChange={(e) => setFormData({ ...formData, merk: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan model E-Seal"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nomor IMEI <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Masukkan nomor IMEI"
                value={formData.noImei}
                onChange={(e) => setFormData({ ...formData, noImei: e.target.value })}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tipe <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.tipe}
                onValueChange={(value) => setFormData({ ...formData, tipe: value })}
                required
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih tipe" />
                </SelectTrigger>
                <SelectContent>
                  {ESEAL_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GPS Device <span className="text-red-500">*</span>
              </label>
              <Select
                key={`gps-select-${gpsDevices.length}-${formData.gpsDeviceId}`} // Force re-render when devices load or value changes
                value={formData.gpsDeviceId}
                onValueChange={(value) => {
                  console.log('🔄 GPS Device selected:', value);
                  const selectedDevice = gpsDevices.find(d => d.deviceId === value);
                  console.log('🔄 Selected device details:', selectedDevice);
                  setFormData({ ...formData, gpsDeviceId: value });
                }}
                required
                disabled={loadingGpsDevices}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={loadingGpsDevices ? "Loading GPS devices..." : "Pilih GPS Device"} />
                </SelectTrigger>
                <SelectContent>
                  {gpsDevices.map((device) => (
                    <SelectItem key={device.deviceId} value={device.deviceId}>
                      <div className="flex flex-col">
                        <span className="font-medium">{device.vehicleName}</span>
                        <span className="text-xs text-gray-500">
                          ID: {device.deviceId} | {device.online ? '🟢 Online' : '🔴 Offline'} |
                          Last: {new Date(device.updateTime).toLocaleString('id-ID')}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                  {gpsDevices.length === 0 && !loadingGpsDevices && (
                    <SelectItem value="" disabled>
                      Tidak ada GPS device tersedia
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                💡 Pilih GPS device yang akan digunakan untuk tracking lokasi E-Seal ini
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Token <span className="text-gray-500">(opsional)</span>
              </label>
              <Input
                placeholder="Masukkan token jika diperlukan"
                value={formData.token}
                onChange={(e) => setFormData({ ...formData, token: e.target.value })}
              />
            </div>
            <div className="flex gap-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                Batal
              </Button>
              <Button type="submit" className="flex-1 bg-slate-800 hover:bg-slate-700 text-white" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEdit ? 'Update' : 'Simpan'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default function DataESeal() {
  const { slug } = useParams<{ slug: string }>();
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);
  const [showDetail, setShowDetail] = useState(false);
  const [selectedESeal, setSelectedESeal] = useState<ESealData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingESeal, setEditingESeal] = useState<ESealData | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const handleAddData = async (formData: ESealFormData, organizationSlug: string) => {
    setIsSubmitting(true);
    console.log(`📝 ${isEditMode ? 'Editing' : 'Adding new'} data:`, formData);
    console.log(`🔍 GPS Device ID from form:`, formData.gpsDeviceId);
    try {
      let response;
      let successMessage;

      if (isEditMode && editingESeal) {
        // Edit mode
        response = await fetch(`/api/eseal/${editingESeal.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, organizationSlug: organizationSlug }),
        });
        successMessage = 'E-Seal berhasil diupdate!';
      } else {
        // Add mode
        response = await fetch('/api/beacukai/eseal/create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, organizationSlug: organizationSlug }),
        });
        successMessage = 'E-Seal berhasil ditambahkan!';
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || `Gagal ${isEditMode ? 'mengupdate' : 'menambahkan'} E-Seal`);
      }

      alert(successMessage);
      setIsModalOpen(false);
      setIsEditMode(false);
      setEditingESeal(null);
      refetch();
    } catch (error) {
      console.error('Error adding/editing E-Seal:', error);
      alert(`Terjadi kesalahan: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditESeal = (eseal: ESealData) => {
    setEditingESeal(eseal);
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setIsEditMode(false);
    setEditingESeal(null);
  };

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
      setCurrentPage(1); // Reset to first page when searching
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Use real API data
  const { data: esealData, total, loading, error, refetch } = useESealData({
    page: currentPage,
    limit: parseInt(entriesPerPage),
    search: debouncedSearch,
    organizationSlug: slug,
  });

  // Use real data from API (filtering is handled by backend)
  const currentData = esealData || [];
  const totalPages = Math.ceil(total / parseInt(entriesPerPage));

  const handleDetail = (id: string) => {
    const eseal = esealData?.find(item => item.id === id);
    if (eseal) {
      setSelectedESeal(eseal);
      setShowDetail(true);
    }
  };

  // Show detail page if detail is active
  if (showDetail && selectedESeal) {
    return (
      <DetailESeal
        esealId={selectedESeal.id}
        onBack={() => {
          setShowDetail(false);
          setSelectedESeal(null);
          refetch();
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Data E-Seal</h1>
        <p className="text-gray-600">Pantau data E-Seal untuk setiap pengiriman.</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        {/* Show entries */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        {/* Search and Add Button */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari data..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-48 sm:w-64"
            />
          </div>
          <Button
            onClick={() => setIsModalOpen(true)}
            className="bg-slate-800 hover:bg-slate-700 text-white"
          >
            <Plus className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Tambah Data</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nama</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor IMEI</TableHead>
                <TableHead className="font-semibold text-slate-700">Organisasi</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor AJU</TableHead>
                <TableHead className="font-semibold text-slate-700">Status</TableHead>
                <TableHead className="font-semibold text-slate-700">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-6 h-6 animate-spin mr-2" />
                      <span className="text-gray-500">Memuat data...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : error ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-red-500">
                    Error: {error}
                  </TableCell>
                </TableRow>
              ) : currentData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data masih kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                currentData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{item.noEseal}</TableCell>
                    <TableCell className="font-mono text-sm">{item.noImei}</TableCell>
                    <TableCell>{item.organization?.name || '-'}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorAJU || '-'}</TableCell>
                    <TableCell>
                      <Badge
                        variant={item.status === 'ACTIVE' ? 'default' : 'secondary'}
                        className={item.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                      >
                        {item.status === 'ACTIVE' ? 'Aktif' : 'Tidak Aktif'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDetail(item.id)}
                          className="text-slate-600 hover:text-slate-800"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          Lihat
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleEditESeal(item)}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="w-4 h-4 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination info */}
      {total > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Showing {((currentPage - 1) * parseInt(entriesPerPage)) + 1} to {Math.min(currentPage * parseInt(entriesPerPage), total)} of {total} entries
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Modal */}
      <TambahDataModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleAddData}
        slug={slug!}
        loading={isSubmitting}
        editData={editingESeal}
        isEdit={isEditMode}
      />
    </div>
  );
}
