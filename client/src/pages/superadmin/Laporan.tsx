import React, { useState } from 'react';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Download, Save, Upload, Filter, List, Columns, MapPin } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import MapTiler from '../../components/map/MapTiler';

// Dummy data untuk laporan sesuai spesifikasi
const dummyLaporanData = [
  {
    id: '1',
    no: 1,
    noESeal: '7848585252',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'UNLOCKED',
    count: 1,
    duration: '01 h 44 m 25 s',
    mileage: '28',
    maxSpeed: '41',
    avgSpeed: '17.73668514',
    startDateTime: '2025-07-07 17:02:18',
    endDateTime: '2025-07-07 17:07:18',
    startMileage: '198',
    endMileage: '200',
    tripMileage: '2',
    tripDuration: '01 h 44 m 25 s',
    driverName: 'Budi Santosa',
    startAddress: 'Kendal, Jawa Tengah',
    endAddress: 'Surabaya, Jawa Timur',
    startLat: '-6.9175',
    startLng: '110.1625',
    endLat: '-7.2575',
    endLng: '112.7521'
  },
  {
    id: '2',
    no: 2,
    noESeal: '7612340022',
    noAju: 'AJU123457684663',
    noIMEI: '875298572967967922',
    event: 'LOCKED',
    count: 1,
    duration: '02 h 15 m 30 s',
    mileage: '35',
    maxSpeed: '55',
    avgSpeed: '22.5',
    startDateTime: '2025-07-08 09:15:30',
    endDateTime: '2025-07-08 11:31:00',
    startMileage: '250',
    endMileage: '285',
    tripMileage: '35',
    tripDuration: '02 h 15 m 30 s',
    driverName: 'Ahmad Wijaya',
    startAddress: 'Semarang, Jawa Tengah',
    endAddress: 'Yogyakarta, DIY',
    startLat: '-6.9667',
    startLng: '110.4167',
    endLat: '-7.7956',
    endLng: '110.3695'
  }
];

// Chart data for visualization
const chartData = [
  { name: '7848585252', mileage: 28, maxSpeed: 41, avgSpeed: 17.7 },
  { name: '7612340022', mileage: 35, maxSpeed: 55, avgSpeed: 22.5 }
];

const Laporan: React.FC = () => {
  const [selectedEseal, setSelectedEseal] = useState('');
  const [date, setDate] = useState('');
  const [exportAddress, setExportAddress] = useState(false);

  const handleDownload = () => {
    if (!selectedEseal || !date) {
      alert('Silakan pilih E-Seal dan tanggal terlebih dahulu.');
      return;
    }
    // Implement download functionality
    console.log('Downloading report...');
  };

  const handleSave = () => {
    // Implement save functionality
    console.log('Saving report...');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Laporan E-Seal</h1>
          <p className="text-gray-600">Laporan tracking dan monitoring E-Seal</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleDownload} variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
          <Button onClick={handleSave} variant="outline" size="sm">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="w-4 h-4 mr-2" />
            Upload
          </Button>
        </div>
      </div>

      {/* Filter Section */}
      <div className="bg-white p-4 rounded-lg border">
        <div className="flex flex-col sm:flex-row gap-4 items-end">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              E-Seal
            </label>
            <Select value={selectedEseal} onValueChange={setSelectedEseal}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih E-Seal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7848585252">7848585252</SelectItem>
                <SelectItem value="7612340022">7612340022</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tanggal
            </label>
            <Input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="exportAddress"
              checked={exportAddress}
              onChange={(e) => setExportAddress(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="exportAddress" className="text-sm text-gray-700">
              Export Address
            </label>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <List className="w-4 h-4 mr-2" />
              List
            </Button>
            <Button variant="outline" size="sm">
              <Columns className="w-4 h-4 mr-2" />
              Columns
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Mileage Chart */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Jarak Tempuh E-Seal</h3>
          <div className="h-64">
            <div className="w-full h-full bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border-2 border-dashed border-blue-200 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-700">Bar Chart - Jarak Tempuh</p>
                <p className="text-xs text-gray-500 mt-1">Perbandingan jarak tempuh antar E-Seal</p>
                <div className="mt-4 flex justify-center space-x-4">
                  {chartData.map((item, index) => (
                    <div key={item.name} className="text-center">
                      <div 
                        className={`w-8 mx-auto rounded-t ${index === 0 ? 'bg-blue-500' : 'bg-purple-500'}`}
                        style={{ height: `${(item.mileage / 50) * 80}px` }}
                      ></div>
                      <p className="text-xs mt-1 font-mono">{item.name.slice(-4)}</p>
                      <p className="text-xs text-gray-600">{item.mileage} Km</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Speed Chart */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Kecepatan E-Seal</h3>
          <div className="h-64">
            <div className="w-full h-full bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border-2 border-dashed border-green-200 flex items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-lg mx-auto mb-3 flex items-center justify-center">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <p className="text-sm font-medium text-gray-700">Line Chart - Kecepatan</p>
                <p className="text-xs text-gray-500 mt-1">Max Speed vs Avg Speed</p>
                <div className="mt-4 space-y-2">
                  {chartData.map((item, index) => (
                    <div key={item.name} className="flex items-center justify-between text-xs">
                      <span className="font-mono">{item.name.slice(-4)}</span>
                      <div className="flex space-x-2">
                        <span className={`px-2 py-1 rounded ${index === 0 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                          Max: {item.maxSpeed} km/h
                        </span>
                        <span className={`px-2 py-1 rounded ${index === 0 ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                          Avg: {item.avgSpeed} km/h
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">No E-Seal</TableHead>
                <TableHead className="font-semibold text-white">No Aju</TableHead>
                <TableHead className="font-semibold text-white">No IMEI</TableHead>
                <TableHead className="font-semibold text-white">Event</TableHead>
                <TableHead className="font-semibold text-white">Count</TableHead>
                <TableHead className="font-semibold text-white">Duration</TableHead>
                <TableHead className="font-semibold text-white">Mileage</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dummyLaporanData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{item.no}</TableCell>
                  <TableCell className="font-mono text-sm">{item.noESeal}</TableCell>
                  <TableCell className="font-mono text-sm">{item.noAju}</TableCell>
                  <TableCell className="font-mono text-sm">{item.noIMEI}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      item.event === 'UNLOCKED' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                    }`}>
                      {item.event}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">{item.count}</TableCell>
                  <TableCell className="font-mono text-sm">{item.duration}</TableCell>
                  <TableCell className="text-center">{item.mileage}</TableCell>
                  <TableCell className="text-center">{item.maxSpeed}</TableCell>
                  <TableCell className="text-center">{item.avgSpeed}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-800">
                <TableHead className="font-semibold text-white">No</TableHead>
                <TableHead className="font-semibold text-white">Start Date Time</TableHead>
                <TableHead className="font-semibold text-white">End Date Time</TableHead>
                <TableHead className="font-semibold text-white">Start Mileage (Km)</TableHead>
                <TableHead className="font-semibold text-white">End Mileage (Km)</TableHead>
                <TableHead className="font-semibold text-white">Trip Mileage (Km)</TableHead>
                <TableHead className="font-semibold text-white">Trip Duration</TableHead>
                <TableHead className="font-semibold text-white">Driver Name</TableHead>
                <TableHead className="font-semibold text-white">Max Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Avg Speed(Km/h)</TableHead>
                <TableHead className="font-semibold text-white">Start Address</TableHead>
                <TableHead className="font-semibold text-white">End Address</TableHead>
                <TableHead className="font-semibold text-white">Start Lat</TableHead>
                <TableHead className="font-semibold text-white">Start Lng</TableHead>
                <TableHead className="font-semibold text-white">End Lat</TableHead>
                <TableHead className="font-semibold text-white">End Lng</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dummyLaporanData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">{item.no}</TableCell>
                  <TableCell className="font-mono text-sm">{item.startDateTime}</TableCell>
                  <TableCell className="font-mono text-sm">{item.endDateTime}</TableCell>
                  <TableCell className="text-center">{item.startMileage}</TableCell>
                  <TableCell className="text-center">{item.endMileage}</TableCell>
                  <TableCell className="text-center">{item.tripMileage}</TableCell>
                  <TableCell className="font-mono text-sm">{item.tripDuration}</TableCell>
                  <TableCell>{item.driverName}</TableCell>
                  <TableCell className="text-center">{item.maxSpeed}</TableCell>
                  <TableCell className="text-center">{item.avgSpeed}</TableCell>
                  <TableCell className="text-sm">{item.startAddress}</TableCell>
                  <TableCell className="text-sm">{item.endAddress}</TableCell>
                  <TableCell className="font-mono text-sm">{item.startLat}</TableCell>
                  <TableCell className="font-mono text-sm">{item.startLng}</TableCell>
                  <TableCell className="font-mono text-sm">{item.endLat}</TableCell>
                  <TableCell className="font-mono text-sm">{item.endLng}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Map Replay Section */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <MapPin className="w-5 h-5 mr-2 text-blue-600" />
            Map Replay - Track History
          </h3>
          <p className="text-sm text-gray-600 mt-1">Visualisasi rute perjalanan E-Seal berdasarkan data tracking</p>
        </div>
        <div className="h-96">
          <MapTiler
            originLocation={{ lat: -6.9175, lng: 110.1625, address: 'Kendal, Jawa Tengah' }}
            destinationLocation={{ lat: -7.2575, lng: 112.7521, address: 'Surabaya, Jawa Timur' }}
            interactive={true}
            height="100%"
          />
        </div>
      </div>
    </div>
  );
};

export default Laporan;
