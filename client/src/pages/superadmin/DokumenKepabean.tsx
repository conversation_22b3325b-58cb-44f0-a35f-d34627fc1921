import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../components/ui/table';
import { Input } from '../../components/ui/input';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Search, Eye, Plus, Edit, FileText } from 'lucide-react';

// Dummy data untuk dokumen kepabean sesuai spesifikasi
const dummyDokumenData = [
  {
    id: '1',
    nomorAju: 'AJU123456789',
    kodeDokumen: 'DOK001',
    nomorDaftar: 'DF001',
    tanggalDaftar: '25 Jan 2025',
    kodeKantor: 'KNT001',
    namaKantor: '<PERSON>ntor <PERSON>a <PERSON>uka<PERSON> Jakarta',
    kodeTPS: 'TPS001',
    namaGudang: 'Gudang Utama',
    idPengusha: 'PGS001',
    namaPengusaha: 'PT. Ekspor Jaya',
    uraian: 'Ekspor Barang Elektronik',
    nomorKontainer: 'CONT123456',
    nomorSegel: 'SGL001'
  }
];

export default function DokumenKepabean() {
  const [search, setSearch] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data berdasarkan search
  const filteredData = dummyDokumenData.filter(item =>
    item.nomorAju.toLowerCase().includes(search.toLowerCase()) ||
    item.kodeDokumen.toLowerCase().includes(search.toLowerCase()) ||
    item.nomorDaftar.toLowerCase().includes(search.toLowerCase()) ||
    item.namaKantor.toLowerCase().includes(search.toLowerCase()) ||
    item.namaPengusaha.toLowerCase().includes(search.toLowerCase()) ||
    item.nomorKontainer.toLowerCase().includes(search.toLowerCase())
  );

  const totalPages = Math.ceil(filteredData.length / parseInt(entriesPerPage));
  const startIndex = (currentPage - 1) * parseInt(entriesPerPage);
  const currentData = filteredData.slice(startIndex, startIndex + parseInt(entriesPerPage));

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Approved':
        return <Badge className="bg-green-100 text-green-800">Disetujui</Badge>;
      case 'Pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Menunggu</Badge>;
      case 'Processing':
        return <Badge className="bg-blue-100 text-blue-800">Diproses</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800">Ditolak</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const handleDetail = (id: string) => {
    console.log('View detail for dokumen:', id);
    // TODO: Implement detail view
  };

  const handleEdit = (id: string) => {
    console.log('Edit dokumen:', id);
    // TODO: Implement edit functionality
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">Dokumen Kepabean</h1>
        <p className="text-gray-600">Kelola dokumen kepabean untuk proses ekspor dan impor.</p>
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white p-4 rounded-lg border">
        {/* Show entries */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Show</span>
          <Select value={entriesPerPage} onValueChange={setEntriesPerPage}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-600">entries per page</span>
        </div>

        {/* Search and Add Button */}
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Cari dokumen..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-48 sm:w-64"
            />
          </div>
          <Button className="bg-slate-800 hover:bg-slate-700 text-white">
            <Plus className="w-4 h-4 sm:mr-2" />
            <span className="hidden sm:inline">Tambah Dokumen</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="font-semibold text-slate-700">Nomor Aju</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode Dokumen</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Daftar</TableHead>
                <TableHead className="font-semibold text-slate-700">Tanggal Daftar</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode Kantor</TableHead>
                <TableHead className="font-semibold text-slate-700">Nama Kantor</TableHead>
                <TableHead className="font-semibold text-slate-700">Kode TPS</TableHead>
                <TableHead className="font-semibold text-slate-700">Nama Gudang</TableHead>
                <TableHead className="font-semibold text-slate-700">ID Pengusha</TableHead>
                <TableHead className="font-semibold text-slate-700">Nama Pengusaha</TableHead>
                <TableHead className="font-semibold text-slate-700">Uraian</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Kontainer</TableHead>
                <TableHead className="font-semibold text-slate-700">Nomor Segel</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8 text-gray-500">
                    {search ? 'Data tidak ditemukan' : 'Data masih kosong'}
                  </TableCell>
                </TableRow>
              ) : (
                currentData.map((item) => (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="font-mono text-sm font-medium">{item.nomorAju}</TableCell>
                    <TableCell className="text-sm">{item.kodeDokumen}</TableCell>
                    <TableCell className="text-sm">{item.nomorDaftar}</TableCell>
                    <TableCell className="text-sm">{item.tanggalDaftar}</TableCell>
                    <TableCell className="text-sm">{item.kodeKantor}</TableCell>
                    <TableCell className="text-sm">{item.namaKantor}</TableCell>
                    <TableCell className="text-sm">{item.kodeTPS}</TableCell>
                    <TableCell className="text-sm">{item.namaGudang}</TableCell>
                    <TableCell className="text-sm">{item.idPengusha}</TableCell>
                    <TableCell className="text-sm">{item.namaPengusaha}</TableCell>
                    <TableCell className="text-sm">{item.uraian}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorKontainer}</TableCell>
                    <TableCell className="font-mono text-sm">{item.nomorSegel}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination info */}
      {filteredData.length > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>
            Showing {startIndex + 1} to {Math.min(startIndex + parseInt(entriesPerPage), filteredData.length)} of {filteredData.length} entries
          </span>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="px-3 py-1 bg-slate-800 text-white rounded text-xs">
              {currentPage}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
